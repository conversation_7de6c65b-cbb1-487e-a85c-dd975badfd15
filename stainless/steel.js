#!/usr/bin/env node

import yargs from 'yargs';
import { hideBin } from 'yargs/helpers';
import chalk from 'chalk';
import { fileURLToPath } from 'url';
import { exec } from 'child_process';
import fs from 'fs-extra';
import path from 'path';
import util from 'util';
import os from 'os';
import AdmZip from 'adm-zip';
import toml from 'toml';
import { glob } from 'glob';
import archiver from 'archiver';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const execAsync = util.promisify(exec);

console.log(chalk.blue('Surreal Steel :: Python Code Transformer'));

const ignoreDirs = ['build', '__pycache__', '.git', '.vscode', 'node_modules', 'wheels', 'dist_zip_stage'];

// Logger that can be silenced by the --compact flag
const logger = {
    info: (...args) => console.log(...args),
    debug: (...args) => console.log(...args),
    warn: (...args) => console.warn(...args),
    error: (...args) => console.error(...args),
    success: (...args) => console.log(...args),
};

async function checkPrerequisites() {
    try {
        await execAsync('python3.11 --version');
    } catch (error) {
        logger.warn(chalk.yellow.bold('Warning: `python3.11` not found. Please ensure Python 3.11 is installed and in your PATH.'));
    }

    try {
        await execAsync('cython --version');
        global.cythonAvailable = true;
        logger.info(chalk.green('Cython is available. Performance extensions will be enabled.'));
    } catch (error) {
        global.cythonAvailable = false;
        logger.warn(chalk.yellow.bold('Warning: `cython` not found. Falling back to pure Python mode.'));
        logger.warn(chalk.yellow('Install Cython for a significant performance boost (`pip install cython`).'));
    }
    logger.info('');
}

async function checkSyntax(filePath) {
    try {
        await execAsync(`python3.11 -m py_compile "${filePath}"`);
        logger.debug(chalk.green(`Syntax check passed for ${path.basename(filePath)}`));
        return true;
    } catch (error) {
        logger.error(chalk.red.bold(`\n--- Syntax Check Error: ${path.basename(filePath)} ---`));
        logger.error(chalk.white(error.stderr.trim()));
        logger.error(chalk.red.bold('--- End Error ---'));
        return false;
    }
}

async function compileToCython(extendedMathPath, outputRoot, options = {}) {
    const baseName = path.basename(extendedMathPath, '.py');
    const setupPyPath = path.join(outputRoot, 'setup.py');
    const relativeExtendedPath = path.relative(outputRoot, extendedMathPath);
    const moduleName = relativeExtendedPath.replace(/\.py$/, '').replace(new RegExp(`\\${path.sep}`, 'g'), '.');
    const escapedPath = relativeExtendedPath.replace(/\\/g, '\\');

    const setupCode = `
from setuptools import setup, Extension, find_packages
from Cython.Build import cythonize

ext_modules = [
    Extension(
        "${moduleName}",
        ["${escapedPath}"]
    )
]

setup(
    packages=find_packages(),
    ext_modules=cythonize(ext_modules, language_level="3"),
    zip_safe=False,
)
    `;

    try {
        logger.info(chalk.blue(`\nCompiling ${path.basename(extendedMathPath)} to Cython extension...`));
        await fs.writeFile(setupPyPath, setupCode.trim());
        const command = `python3.11 setup.py build_ext --inplace`;
        await execAsync(command, { cwd: outputRoot });
        logger.success(chalk.green.bold(`Successfully compiled ${baseName} to a native module!`));

        if (options.clean) {
            await fs.remove(extendedMathPath);
            logger.debug(chalk.gray(`Removed temporary source (due to --clean): ${path.basename(extendedMathPath)}`));
        } else {
            logger.debug(chalk.gray(`Kept intermediate source: ${path.basename(extendedMathPath)}`));
        }
    } catch (error) {
        logger.error(chalk.yellow.bold(`\n--- Cython Compilation Failed for ${baseName} ---`));
        logger.error(chalk.white(error.stderr || error.message));
        logger.warn(chalk.yellow('Falling back to standard Python module.'));
    } finally {
        const dirName = path.dirname(extendedMathPath);
        const cFile = path.join(dirName, `${baseName}.c`);
        await fs.remove(setupPyPath);
        await fs.remove(path.join(outputRoot, 'build'));
        if (await fs.pathExists(cFile)) {
            await fs.remove(cFile);
        }
    }
}

async function processFile(filePath, outputFilePath, options, rootInputDir) {
    const relativePath = path.relative(rootInputDir, filePath);
    const baseName = path.basename(filePath, '.py');
    const outputDir = path.dirname(outputFilePath);
    await fs.ensureDir(outputDir);

    // If the file is `__init__.py`, use a non-conflicting name for the extensions
    // to avoid import issues inside packages.
    const extendedMathName = baseName === '__init__' 
        ? '_steel_init_helpers' 
        : `${baseName}_extended`;
    const extendedMathPath = path.join(outputDir, `${extendedMathName}.py`);

    try {
        await fs.writeFile(extendedMathPath, '');
    } catch (err) {
        logger.error(chalk.red(`Could not create ${extendedMathPath}:`, err));
        return;
    }

    const transformCommand = `python3.11 transform.py "${filePath}" "${outputFilePath}" "${extendedMathName}" "${relativePath}"`;
    logger.info(chalk.blue(`\nTransforming: ${path.basename(filePath)}`));

    const { newFunctions } = await new Promise((resolve) => {
        exec(transformCommand, (error, stdout, stderr) => {
            if (error) {
                logger.error(chalk.red.bold(`\n--- Transformation Error: ${path.basename(filePath)} ---`));
                logger.error(chalk.white(stderr.trim()));
                return resolve({ newFunctions: [] });
            }
            const functions = [];
            const outputLines = stdout.split('\n');
            const logOutput = [];
            let isInsideFuncDef = false;
            let currentFunc = [];
            outputLines.forEach(line => {
                if (line.trim() === 'STEEL_FUNC_DEF_START') isInsideFuncDef = true;
                else if (line.trim() === 'STEEL_FUNC_DEF_END') {
                    isInsideFuncDef = false;
                    functions.push(currentFunc.join('\n'));
                    currentFunc = [];
                } else if (isInsideFuncDef) currentFunc.push(line);
                else if (line.trim()) logOutput.push(line);
            });
            logger.info(chalk.green(logOutput.join('\n')));
            resolve({ newFunctions: functions });
        });
    });

    if (newFunctions && newFunctions.length > 0) {
        const uniqueFunctions = Array.from(new Set(newFunctions));
        const header = `# -- Dynamically generated functions for ${path.basename(filePath)} --\n\n`;
        const functionsCode = uniqueFunctions.join('\n\n');
        await fs.appendFile(extendedMathPath, header + functionsCode + '\n');
        logger.info(chalk.blue(`Appended ${uniqueFunctions.length} unique functions to ${path.basename(extendedMathPath)}`));
        
        logger.info(chalk.blue('\nRunning syntax checks...'));
        const transformedOk = await checkSyntax(outputFilePath);
        const extendedOk = await checkSyntax(extendedMathPath);

        // If we are not packaging, we can compile the extension right away.
        // If we are packaging, we must leave the .py file for setup.py to find and compile.
        // If we are using --package-direct, we also compile right away since we're not creating wheels.
        if (transformedOk && extendedOk && global.cythonAvailable && (!options.package || options.packageDirect)) {
            await compileToCython(extendedMathPath, outputDir, options);
        }
    } else {
        await fs.remove(extendedMathPath);
        logger.debug(chalk.gray(`No specialized functions needed for ${path.basename(filePath)}. Cleaned up.`));
        await checkSyntax(outputFilePath);
    }
}

async function processDirectory(sourceDir, outputDir, options = {}, rootSourceDir = null) {
    if (rootSourceDir === null) {
        rootSourceDir = sourceDir;
    }
    await fs.ensureDir(outputDir);
    const entries = await fs.readdir(sourceDir, { withFileTypes: true });

    for (const entry of entries) {
        const sourcePath = path.join(sourceDir, entry.name);
        const destPath = path.join(outputDir, entry.name);

        if (ignoreDirs.includes(entry.name)) continue;

        if (entry.isDirectory()) {
            await processDirectory(sourcePath, destPath, options, rootSourceDir);
        } else if (entry.isFile()) {
            if (entry.name === 'steel_lib.py') {
                await fs.copy(sourcePath, destPath);
                logger.debug(chalk.gray(`Copied library file: ${entry.name}`));
            } else if (entry.name.endsWith('.py')) {
                await processFile(sourcePath, destPath, options, rootSourceDir);
            } else {
                await fs.copy(sourcePath, destPath);
                logger.debug(chalk.gray(`Copied non-python file: ${entry.name}`));
            }
        }
    }
}

async function cleanupPycache(directory) {
    if (!await fs.pathExists(directory)) return;
    const entries = await fs.readdir(directory, { withFileTypes: true });
    for (const entry of entries) {
        const fullPath = path.join(directory, entry.name);
        if (entry.isDirectory()) {
            if (entry.name === '__pycache__') {
                await fs.remove(fullPath);
            } else {
                await cleanupPycache(fullPath);
            }
        }
    }
}

async function updateManifestWheels(manifestPath, wheelPathInManifest) {
    logger.info(chalk.blue(`\nUpdating manifest: ${path.basename(manifestPath)}`));
    try {
        let content = await fs.readFile(manifestPath, 'utf-8');
        const wheelsRegex = /^\s*wheels\s*=\s*\[([^\]]*)\]/m;
        const match = content.match(wheelsRegex);

        if (match) {
            // 'wheels' array exists, append to it
            const existingWheels = match[1].trim();
            const newWheelsList = existingWheels
                ? `${existingWheels}, \"${wheelPathInManifest}\"`
                : `\"${wheelPathInManifest}\"`;
            const newWheelsString = `wheels = [${newWheelsList}]`;
            content = content.replace(wheelsRegex, newWheelsString);
        } else {
            // 'wheels' array does not exist, create it and insert it
            const wheelString = `wheels = [\"${wheelPathInManifest}\"]`;
            const licenseRegex = /(^\s*license\s*=\s*\[[^]*?](?:\s*\r?\n)?)/m;
            const permissionsRegex = /(^\s*\[permissions].*)/m;

            let inserted = false;
            if (licenseRegex.test(content)) {
                content = content.replace(licenseRegex, `$1\n${wheelString}\n`);
                inserted = true;
            } else if (permissionsRegex.test(content)) {
                content = content.replace(permissionsRegex, `\n${wheelString}\n$1`);
                inserted = true;
            }

            if (!inserted) {
                content += `\n${wheelString}\n`;
            }
        }

        await fs.writeFile(manifestPath, content.trim() + '\n');
        logger.success(chalk.green.bold('Successfully updated blender_manifest.toml.'));
    } catch (e) {
        logger.error(chalk.red('Error updating manifest file:'), e.message);
        throw e;
    }
}

async function cleanWheelSourceFiles(wheelPath, extendedPyFiles) {
    logger.info(chalk.blue('Cleaning ALL Python source files from wheel...'));
    const tempDir = path.join(path.dirname(wheelPath), 'wheel_clean_temp');

    try {
        // Create temporary directory for wheel extraction
        await fs.ensureDir(tempDir);

        // Extract the wheel (wheels are just zip files)
        const AdmZip = (await import('adm-zip')).default;
        const zip = new AdmZip(wheelPath);
        zip.extractAllTo(tempDir, true);

        // Recursively remove ALL Python source files from the entire wheel
        const removeAllPyFiles = async (dirPath) => {
            const entries = await fs.readdir(dirPath, { withFileTypes: true });
            for (const entry of entries) {
                const fullPath = path.join(dirPath, entry.name);
                if (entry.isDirectory()) {
                    await removeAllPyFiles(fullPath);
                } else if (entry.name.endsWith('.py')) {
                    await fs.remove(fullPath);
                    const relativePath = path.relative(tempDir, fullPath);
                    logger.debug(chalk.gray(`Removed from wheel: ${relativePath}`));
                }
            }
        };

        await removeAllPyFiles(tempDir);

        // Rebuild the wheel
        const newZip = new AdmZip();
        const addDirectory = (dirPath, zipPath = '') => {
            const entries = fs.readdirSync(dirPath, { withFileTypes: true });
            for (const entry of entries) {
                const fullPath = path.join(dirPath, entry.name);
                const zipEntryPath = zipPath ? path.join(zipPath, entry.name) : entry.name;

                if (entry.isDirectory()) {
                    addDirectory(fullPath, zipEntryPath);
                } else {
                    newZip.addLocalFile(fullPath, zipPath, entry.name);
                }
            }
        };

        addDirectory(tempDir);
        newZip.writeZip(wheelPath);

        logger.success(chalk.green('Successfully cleaned wheel of ALL Python source files'));

    } catch (error) {
        logger.error(chalk.red('Error cleaning wheel source files:'), error.message);
    } finally {
        // Clean up temporary directory
        if (await fs.pathExists(tempDir)) {
            await fs.remove(tempDir);
        }
    }
}

async function packageProject(sourceDir, outputDir, manifest, options) {
    logger.info(chalk.blue('\nStarting packaging process...'));
    const packageId = manifest.id;
    const stagingDir = path.join(outputDir, 'packaging_stage');
    const stagingSetupPyPath = path.join(stagingDir, 'setup.py');
    let success = false;
    let extendedPyFiles = [];

    try {
        await fs.emptyDir(stagingDir);
        const sourcePackageDir = path.join(outputDir, packageId);

        // Copy the core library to the output package directory so it can be compiled.
        // Use the script's directory, not the current working directory
        const scriptDir = path.dirname(new URL(import.meta.url).pathname);
        const steelLibSourcePath = path.resolve(scriptDir, 'steel_lib.py');
        const steelLibDestPath = path.join(sourcePackageDir, 'steel_lib.py');
        if (await fs.pathExists(steelLibSourcePath)) {
            await fs.copy(steelLibSourcePath, steelLibDestPath);
            logger.debug(chalk.gray('Copied steel_lib.py to staging package directory.'));
        }

        // Find all original python files and copy them to the staging package dir
        const pyFiles = await glob('**/*.py', { cwd: sourcePackageDir, nodir: true });
        const stagingPackageDir = path.join(stagingDir, packageId);
        await fs.ensureDir(stagingPackageDir);
        for (const pyFile of pyFiles) {
            await fs.copy(path.join(sourcePackageDir, pyFile), path.join(stagingPackageDir, pyFile));
        }

        // Find all generated python files to be compiled
        extendedPyFiles = pyFiles.filter(f => f.endsWith('_extended.py') || f.endsWith('_steel_init_helpers.py') || f === 'steel_lib.py');
        const extensions = extendedPyFiles.map(file => {
            // e.g., from 'a_extended.py' to 'YES.a_extended'
            const moduleName = `${packageId}.${file.replace(/\//g, '.').slice(0, -3)}`;
            // source path is relative to the setup.py file, e.g., 'YES/a_extended.py'
            const sourcePath = path.join(packageId, file);
            return `Extension("${moduleName}", ["${sourcePath}"])`;
        });

        const setupCode = `
from setuptools import setup, find_packages, Extension
from Cython.Build import cythonize

extensions = [
    ${extensions.join(',\n    ')}
]

setup(
    name="${manifest.id}",
    version="${manifest.version}",
    author="${manifest.maintainer}",
    description="${manifest.tagline}",
    packages=find_packages(),
    ext_modules=cythonize(extensions, compiler_directives={'language_level' : "3"}),
    zip_safe=False,
)
        `.trim();

        await fs.writeFile(stagingSetupPyPath, setupCode);

        logger.info(chalk.blue('Building wheel from temporary stage...'));
        // We run bdist_wheel directly, which is the command for building wheels with extensions
        await execAsync('python3.11 setup.py bdist_wheel', { cwd: stagingDir });

        const distDir = path.join(stagingDir, 'dist');
        const wheelFile = (await fs.readdir(distDir)).find(f => f.endsWith('.whl'));
        if (!wheelFile) throw new Error('Wheel file not found after build.');

        const wheelsDir = path.join(outputDir, packageId, 'wheels');
        await fs.ensureDir(wheelsDir);
        const finalWheelPath = path.join(wheelsDir, path.basename(wheelFile));
        await fs.move(path.join(distDir, wheelFile), finalWheelPath, { overwrite: true });
        logger.success(chalk.green('Successfully created wheel package at:'), finalWheelPath);

        // If --use-wheels-dir or --package-wheel is enabled, clean the Python source files from the wheel
        if (options.useWheelsDir || options.packageWheel) {
            await cleanWheelSourceFiles(finalWheelPath, extendedPyFiles);
        }

        if (options.updateManifest) {
            const outputManifestPath = path.join(outputDir, packageId, 'blender_manifest.toml');
            if (!await fs.pathExists(outputManifestPath)) {
                logger.warn(chalk.yellow('Manifest file not found in output directory, skipping update.'));
            } else {
                const manifestWheelPath = `./wheels/${path.basename(finalWheelPath)}`;
                logger.info(chalk.blue('Updating manifest in output directory:', path.relative(process.cwd(), outputManifestPath)));
                await updateManifestWheels(outputManifestPath, manifestWheelPath);
                logger.success(chalk.green('Successfully updated manifest in output directory.'));
            }
        }
        success = true;
    } catch (e) {
        logger.error(chalk.red('Error during packaging process:'), e.message);
        logger.error(e.stack);
    } finally {
        if (await fs.pathExists(stagingDir)) await fs.remove(stagingDir);
    }

    // If cleaning is enabled, remove the intermediate python source files that were compiled into extensions.
    // This happens after the wheel is successfully built.
    if (success && options.clean) {
        logger.debug(chalk.gray('\nCleaning up temporary python extensions...'));
        try {
            const sourcePackageDir = path.join(outputDir, packageId);
            // Remove all the python files that were compiled into extensions from the package directory
            for (const pyFile of extendedPyFiles) {
                const filePath = path.join(sourcePackageDir, pyFile);
                if (await fs.pathExists(filePath)) {
                    await fs.remove(filePath);
                    logger.debug(chalk.gray(`Cleaned up compiled source: ${pyFile}`));
                }
            }

            // Also clean up any steel_lib.py that might have been accidentally copied to the source directory
            const sourceLibPath = path.join(sourceDir, 'steel_lib.py');
            if (await fs.pathExists(sourceLibPath)) {
                await fs.remove(sourceLibPath);
                logger.debug(chalk.gray('Cleaned up steel_lib.py from source directory'));
            }
        } catch (e) {
            logger.error(chalk.red('Error during post-package cleanup:'), e.message);
        }
    }
}

async function createDistributionZip(outputDir, packageId, version) {
    logger.info(chalk.blue('\nCreating distribution zip archive...'));
    const zipFileName = `${packageId}${version ? `-${version}` : ''}.zip`;
    const finalZipPath = path.join(outputDir, zipFileName);
    const sourcePath = path.join(outputDir, packageId);

    // Ensure the source directory for zipping exists
    if (!await fs.pathExists(sourcePath)) {
        logger.error(chalk.red(`Cannot create zip. Source directory not found: ${sourcePath}`));
        return null;
    }

    // Overwrite existing zip file in the output directory
    if (await fs.pathExists(finalZipPath)) {
        await fs.remove(finalZipPath);
    }

    try {
        const output = fs.createWriteStream(finalZipPath);
        const archive = archiver('zip', { zlib: { level: 9 } });

        await new Promise((resolve, reject) => {
            output.on('close', resolve);
            archive.on('error', reject);
            archive.pipe(output);
            archive.directory(sourcePath, packageId);
            archive.finalize();
        });

        logger.success(chalk.green.bold('Successfully created distribution zip at:'), finalZipPath);
        return finalZipPath;
    } catch (e) {
        logger.error(chalk.red('Error creating distribution zip:'), e.message);
        return null;
    }
}

async function findManifest(startPath) {
    // Search recursively for the manifest file.
    const files = await glob('**/blender_manifest.toml', { cwd: startPath, nodir: true });
    if (files.length > 0) {
        // Prefer the shallowest manifest file found.
        files.sort((a, b) => a.split(path.sep).length - b.split(path.sep).length);
        const manifestPath = path.join(startPath, files[0]);
        logger.info(chalk.green(`Found manifest at: ${path.relative(process.cwd(), manifestPath)}`));
        return manifestPath;
    }
    return null;
}

function getAddonIdFromZip(zipPath) {
    try {
        const zip = new AdmZip(zipPath);
        const zipEntries = zip.getEntries();

        // 1. Check for modern addon (blender_manifest.toml)
        const manifestEntry = zipEntries.find(e => path.basename(e.entryName) === 'blender_manifest.toml');
        if (manifestEntry) {
            const manifestContent = zip.readAsText(manifestEntry);
            const manifest = toml.parse(manifestContent);
            if (manifest.id) {
                logger.info(chalk.green(`Found modern addon ID from manifest: ${manifest.id}`));
                return { id: manifest.id, isLegacy: false };
            }
        }

        // 2. Check for legacy addon (__init__.py)
        const initPyEntry = zipEntries.find(e => path.basename(e.entryName) === '__init__.py');
        if (initPyEntry) {
            // The addon ID is the name of the directory containing __init__.py
            const entryPath = initPyEntry.entryName.replace(/\\/g, '/');
            const pathParts = entryPath.split('/').filter(p => p);
            
            if (pathParts.length > 1) { // e.g., 'my_addon/__init__.py'
                const id = pathParts[pathParts.length - 2];
                logger.info(chalk.green(`Found legacy addon ID from __init__.py: ${id}`));
                return { id: id, isLegacy: true };
            }
        }
        
        // 3. Fallback for zips with a single root folder (might be a legacy addon)
        const rootFolders = new Set();
        zipEntries.forEach(entry => {
            const parts = entry.entryName.replace(/\/$/, '').split('/');
            if (parts.length > 0 && parts[0]) {
                rootFolders.add(parts[0]);
            }
        });

        const finalFolders = [...rootFolders].filter(f => f !== '__MACOSX');
        if (finalFolders.length === 1) {
            const id = finalFolders[0];
            logger.info(chalk.green(`Found legacy addon ID from single root folder: ${id}`));
            return { id: id, isLegacy: true };
        }


    } catch (e) {
        logger.error(`Error reading addon ID from ${zipPath}: ${e.message}`);
    }
    
    logger.warn(chalk.yellow(`Could not determine addon ID for ${path.basename(zipPath)}.`));
    return null;
}

async function blenderCheck(zipPath, packageId, isLegacy = false) {
    logger.info(chalk.cyan(`\n--- Running Blender Check for ${packageId} (${isLegacy ? 'Legacy' : 'Modern'}) ---`));
    const escapedPath = zipPath.replace(/"/g, '\"');
    let command;

    if (isLegacy) {
        command = `blender --background --factory-startup --python-expr "import bpy; bpy.ops.preferences.addon_install(overwrite=True, filepath='${escapedPath}'); bpy.ops.preferences.addon_enable(module='${packageId}'); bpy.ops.wm.save_userpref()"`;
    } else {
        command = `blender --factory-startup --command extension install-file -r user_default --enable "${escapedPath}"`;
    }

    logger.info(`Executing Blender check command: ${command}`);
    try {
        const { stdout, stderr } = await execAsync(command);

        // Check for specific errors in stderr, but ignore common non-fatal warnings.
        if (stderr && /error/i.test(stderr) && !/already registered/i.test(stderr)) {
            throw new Error(stderr);
        }

        // The command's output can also contain error indicators.
        if (stdout.includes('ERROR')) {
            throw new Error(stdout);
        }

        logger.success(chalk.green.bold(`Blender Check Passed for ${packageId}`));
        logger.debug(stdout);

    } catch (e) {
        logger.error(chalk.red.bold(`--- Blender Check Failed for ${packageId} ---`));
        logger.error(chalk.red(e.message));
    }
}

async function generateDirectImportLogic(outputDir, packageId, baseNames) {
    logger.info(chalk.blue('Generating direct import logic for compiled extensions...'));

    const packageDir = path.join(outputDir, packageId);
    const initFilePath = path.join(packageDir, '__init__.py');

    // Check if __init__.py exists
    if (!await fs.pathExists(initFilePath)) {
        logger.warn(chalk.yellow(`No __init__.py found at ${initFilePath}. Skipping direct import logic generation.`));
        return;
    }

    // Read the current __init__.py content
    let initContent = await fs.readFile(initFilePath, 'utf8');

    // Check if importlib logic already exists
    if (initContent.includes('# Auto-generated importlib logic for compiled extensions')) {
        logger.info(chalk.yellow('Direct import logic already exists in __init__.py. Skipping generation.'));
        return;
    }

    // Generate importlib logic for each compiled extension
    const importLogicParts = [];

    // Generate a single, efficient importlib logic block for all extensions
    // baseNames is already provided as parameter

    importLogicParts.push(`
# Auto-generated importlib logic for compiled extensions
try:
    import importlib.util
    import sys
    import os
    from pathlib import Path

    addon_dir = Path(__file__).parent

    # List of expected compiled extensions (without platform-specific suffixes)
    expected_extensions = ${JSON.stringify(baseNames)}

    for base_name in expected_extensions:
        extension_path = None

        # Look for compiled extension files with various suffixes
        for ext_file in addon_dir.glob(f'{base_name}.*'):
            if ext_file.suffix in ['.so', '.pyd', '.dylib'] or '.so' in ext_file.name:
                extension_path = str(ext_file)
                break

        if extension_path:
            try:
                spec = importlib.util.spec_from_file_location(base_name, extension_path)
                if spec and spec.loader:
                    module = importlib.util.module_from_spec(spec)
                    sys.modules[base_name] = module
                    spec.loader.exec_module(module)

                    # Import all public attributes from the compiled module
                    for attr_name in dir(module):
                        if not attr_name.startswith('_'):
                            globals()[attr_name] = getattr(module, attr_name)
                else:
                    print(f'Warning: Could not create spec for {extension_path}')
            except Exception as e:
                print(f'Warning: Failed to load {base_name} compiled extension: {e}')
        else:
            print(f'Warning: Could not find compiled extension file for {base_name}')

except ImportError as ie:
    print(f'Warning: Failed to import required modules for extension loading: {ie}')
`);

    // Find the position to insert the import logic (after existing imports but before other code)
    const lines = initContent.split('\n');
    let insertPos = 0;

    // Find the last import statement or the first non-import/non-comment line
    for (let i = 0; i < lines.length; i++) {
        const line = lines[i].trim();
        if (line.startsWith('import ') || line.startsWith('from ') ||
            line.startsWith('#') || line === '' || line.startsWith('"""') || line.startsWith("'''")) {
            insertPos = i + 1;
        } else {
            break;
        }
    }

    // Insert the import logic
    const beforeLines = lines.slice(0, insertPos);
    const afterLines = lines.slice(insertPos);
    const newContent = [...beforeLines, ...importLogicParts, ...afterLines].join('\n');

    // Write the updated __init__.py
    await fs.writeFile(initFilePath, newContent);
    logger.success(chalk.green(`Successfully added direct import logic to ${path.relative(process.cwd(), initFilePath)}`));
}

async function run(options) {
    const { input, clean, package: pkg, updateManifest, useWheelsDir, packageWheel, packageDirect, batchFolders, compact } = options;

    if (compact) {
        logger.debug = () => {};
        logger.info = () => {};
    }

    // Validate conflicting options
    if (packageWheel && packageDirect) {
        logger.error(chalk.red.bold('\n❌ ERROR: --package-wheel and --package-direct cannot be used together.'));
        logger.error(chalk.red('   --package-wheel creates wheel packages with importlib logic'));
        logger.error(chalk.red('   --package-direct adds importlib logic for direct extension loading'));
        logger.error(chalk.red('   Choose one approach for your use case.\n'));
        process.exit(1);
    }

    // Handle deprecation warnings and combined argument logic
    let shouldPackage = pkg || packageWheel;
    let shouldUpdateManifest = updateManifest || packageWheel;
    let shouldUseWheelsDir = useWheelsDir || packageWheel;
    // --package-wheel automatically enables cleanup to remove steel_lib.py and other temp files
    // --package-direct also enables cleanup to remove unnecessary Python source files
    let shouldClean = clean || packageWheel || packageDirect;

    // Show deprecation warnings if old flags are used
    if (pkg && !packageWheel) {
        logger.warn(chalk.yellow.bold('\n⚠️  DEPRECATION WARNING: --package flag is deprecated.'));
        logger.warn(chalk.yellow('   Use --package-wheel instead for complete wheel packaging with wheels/ directory and manifest updates.'));
        logger.warn(chalk.yellow('   Migration: Replace --package --use-wheels-dir --update-manifest with --package-wheel\n'));
    }
    if (updateManifest && !packageWheel) {
        logger.warn(chalk.yellow.bold('\n⚠️  DEPRECATION WARNING: --update-manifest flag is deprecated.'));
        logger.warn(chalk.yellow('   Use --package-wheel instead for complete wheel packaging with wheels/ directory and manifest updates.'));
        logger.warn(chalk.yellow('   Migration: Replace --package --use-wheels-dir --update-manifest with --package-wheel\n'));
    }
    if (useWheelsDir && !packageWheel) {
        logger.warn(chalk.yellow.bold('\n⚠️  DEPRECATION WARNING: --use-wheels-dir flag is deprecated.'));
        logger.warn(chalk.yellow('   Use --package-wheel instead for complete wheel packaging with wheels/ directory and manifest updates.'));
        logger.warn(chalk.yellow('   Migration: Replace --package --use-wheels-dir --update-manifest with --package-wheel\n'));
    }

    // Update options object to use the resolved values
    options.package = shouldPackage;
    options.updateManifest = shouldUpdateManifest;
    options.useWheelsDir = shouldUseWheelsDir;
    options.packageDirect = packageDirect;

    if (batchFolders) {
        logger.success(chalk.cyan.bold(`\n--- Batch Mode Enabled: Processing all subfolders in ${input} ---`));

        // Clean the main output directory ONCE for the entire batch.
        if (clean) {
            const absoluteInputPath = path.resolve(input);
            // Determine the top-level output directory for the batch.
            const outputDir = options.output ? path.resolve(options.output) : path.join(process.cwd(), `${path.basename(absoluteInputPath)}_steel`);
            logger.warn(chalk.yellow(`--clean flag detected for batch mode. Removing main output directory: ${outputDir}`));
            await fs.remove(outputDir);
        }

        const subfolders = (await fs.readdir(input, { withFileTypes: true }))
            .filter(dirent => dirent.isDirectory() && !ignoreDirs.includes(dirent.name))
            .map(dirent => dirent.name);

        for (const folder of subfolders) {
            const folderPath = path.join(input, folder);
            logger.success(chalk.magenta.bold(`\n\n====================================================`));
            logger.success(chalk.magenta.bold(`Processing Addon: ${folder}`));
            logger.success(chalk.magenta.bold(`====================================================\n`));
            // Pass clean: false to sub-runs to prevent re-cleaning.
            const singleOptions = { ...options, input: folderPath, batchFolders: false, clean: false };
            await run(singleOptions);
        }
        logger.success(chalk.cyan.bold(`\n--- Batch Mode Finished ---`));
        return;
    }

    await checkPrerequisites();

    let absoluteInputPath = path.resolve(options.input);
    if (!await fs.pathExists(absoluteInputPath)) {
        return logger.error(chalk.red(`Input path not found: ${absoluteInputPath}`));
    }

    const stats = await fs.stat(absoluteInputPath);
    if (!stats.isDirectory()) {
        return logger.error(chalk.red('Input must be a directory.'));
    }

    const outputDir = options.output ? path.resolve(options.output) : path.join(process.cwd(), `${path.basename(absoluteInputPath)}_steel`);
    if (absoluteInputPath === outputDir) {
        return logger.error(chalk.red.bold('Error: Input and output directories cannot be the same.'));
    }

    let manifestPath = await findManifest(absoluteInputPath);
    let manifest = null;
    let packageId;

    if (manifestPath) {
        // Modern addon with a manifest
        try {
            manifest = toml.parse(await fs.readFile(manifestPath, 'utf-8'));
            if (!manifest.id) throw new Error('Manifest must contain an `id` field.');
            packageId = manifest.id;
            const newRoot = path.dirname(manifestPath);
            if (newRoot !== absoluteInputPath) {
                 logger.info(chalk.blue(`Adjusting addon root to manifest location: ${path.relative(process.cwd(), newRoot)}`));
                 absoluteInputPath = newRoot;
            }
        } catch (e) {
            return logger.error(chalk.red('Error parsing blender_manifest.toml:'), e.message);
        }
    } else {
        // Legacy addon without a manifest
        packageId = path.basename(absoluteInputPath);
        logger.info(chalk.blue(`No blender_manifest.toml found. Processing '${packageId}' as a legacy addon.`));
        if (options.package) {
            const flagUsed = packageWheel ? '--package-wheel' : '--package';
            logger.warn(chalk.yellow.bold(`\nWarning: The ${flagUsed} flag is not supported for legacy addons.`));
            logger.warn(chalk.yellow(`Skipping packaging for '${packageId}'.\n`));
            options.package = false;
            options.updateManifest = false;
            options.useWheelsDir = false;
        }
    }

    if (options.clean) {
        logger.warn(chalk.yellow(`--clean flag detected. Removing output directory: ${outputDir}`));
        await fs.remove(outputDir);
    }

    const outputDirForPackage = path.join(outputDir, packageId);

    logger.warn(chalk.yellow(`Processing directory: ${absoluteInputPath}`));
    logger.info(chalk.blue('Output will be written to:'), outputDirForPackage);

    const sourceWheelsDir = path.join(absoluteInputPath, 'wheels');
    if (await fs.pathExists(sourceWheelsDir)) {
        logger.info(chalk.blue('Copying existing wheels directory...'));
        const outputWheelsDir = path.join(outputDirForPackage, 'wheels');
        await fs.copy(sourceWheelsDir, outputWheelsDir);
    }

    // Create resolved options object with combined flag logic
    const resolvedOptions = {
        ...options,
        package: shouldPackage,
        updateManifest: shouldUpdateManifest,
        useWheelsDir: shouldUseWheelsDir,
        packageWheel: packageWheel,
        packageDirect: packageDirect,
        clean: shouldClean
    };

    await processDirectory(absoluteInputPath, outputDirForPackage, resolvedOptions);

    // Handle --package-direct: generate importlib logic for direct extension loading
    if (resolvedOptions.packageDirect) {
        // Find all compiled extension files that should have been created
        const packageDir = path.join(outputDir, packageId);
        const compiledFiles = await glob('**/*.{so,pyd,dylib}', { cwd: packageDir, nodir: true });

        if (compiledFiles.length > 0) {
            // Extract base names from compiled files (remove platform-specific suffixes)
            const baseNames = compiledFiles.map(file => {
                const fileName = path.basename(file);
                // Remove platform-specific suffixes like .cpython-311-x86_64-linux-gnu.so
                // to get back to the original base name like a_extended or _steel_init_helpers
                return fileName.split('.')[0];
            });

            await generateDirectImportLogic(outputDir, packageId, baseNames);
        } else {
            logger.warn(chalk.yellow('No compiled extension files found for direct import logic generation.'));
        }
    }

    logger.info(chalk.blue('\nRunning final cleanup...'));
    await cleanupPycache(outputDir);

    if (resolvedOptions.package) {
        // This will only run if a manifest was found and parsed
        await packageProject(absoluteInputPath, outputDir, manifest, resolvedOptions);
    }

    let zipPath = null;
    if (options.zip) {
        zipPath = await createDistributionZip(outputDir, packageId, manifest?.version);

        if (zipPath && options.cleanAfterZip) {
            const buildDir = path.join(outputDir, packageId);
            logger.warn(chalk.yellow(`--clean-after-zip enabled. Removing build directory: ${buildDir}`));
            await fs.remove(buildDir);
        }
    }

    if (options.blenderCheck) {
        if (!zipPath) {
            logger.warn(chalk.yellow('\n--blender-check requires --zip to be enabled. Zipping now...'));
            zipPath = await createDistributionZip(absoluteInputPath, outputDir, packageId, manifest.version, options.updateManifest);
        }
        if (zipPath) {
            await blenderCheck(zipPath, packageId);
        }
    }

    logger.success(chalk.magenta.bold('\nTransformation process finished.'));
}

yargs(hideBin(process.argv))
    .command('$0 <input>', 'Transform and package a Python project for Blender.', (yargs) => {
        return yargs
            .positional('input', { describe: 'Path to the project directory', type: 'string' })
            .option('output', { alias: 'o', describe: 'Output directory for transformed files', type: 'string' })
            .option('compact', { type: 'boolean', description: 'Show a more compact output, hiding non-essential logs.' })
            .option('batch-folders', { type: 'boolean', description: 'Treat each subfolder in the input path as a separate addon to be processed.' })
            .option('clean', { describe: 'Perform a clean build, removing old output and artifacts', type: 'boolean', default: false })
            .option('package-wheel', { describe: 'Package the compiled extensions into a wheel, move it to wheels/ directory, clean ALL Python source files from wheel, update blender_manifest.toml, and automatically clean up temporary files', type: 'boolean', default: false })
            .option('package-direct', { describe: 'Enable direct use of Cython-compiled extensions without wheel packaging by adding importlib logic for direct importing of compiled extensions', type: 'boolean', default: false })
            .option('package', { describe: '[DEPRECATED] Package the compiled extensions into a wheel (use --package-wheel instead)', type: 'boolean', default: false })
            .option('update-manifest', { describe: '[DEPRECATED] Update blender_manifest.toml with the new wheel path (use --package-wheel instead)', type: 'boolean', default: false })
            .option('use-wheels-dir', { describe: '[DEPRECATED] Move the final wheel to a `wheels/` directory and update the manifest path accordingly (use --package-wheel instead)', type: 'boolean', default: false })
            .option('remove-originals', { alias: 'rm', type: 'boolean', description: 'Remove original .py files after successful Cython compilation.' })
            .option('zip', {
            describe: 'Create a final distribution zip archive',
            type: 'boolean',
            default: false
        })
        .option('clean-after-zip', {
            describe: 'Remove the output directory after successful zip packaging.',
            type: 'boolean',
            default: false
        })
        .option('blender-check', { describe: 'Install and verify the addon in a headless Blender instance after processing', type: 'boolean', default: false });
    }, async (argv) => {
        await run(argv);
    })
    .command('check <path>', 'Run blender-check on an existing addon zip/wheel file or a directory of addons.', (yargs) => {
        return yargs.positional('path', {
            describe: 'Path to the addon file or directory',
            type: 'string'
        });
    }, async (argv) => {
        logger.info(chalk.cyan.bold(`--- Standalone Blender Check Mode ---`));
        await checkPrerequisites();
        const inputPath = path.resolve(argv.path);

        if (!await fs.pathExists(inputPath)) {
            return logger.error(chalk.red(`Input path not found: ${inputPath}`));
        }

        async function checkSourceDirectory(sourcePath) {
            const addonId = path.basename(sourcePath);
            logger.info(chalk.magenta(`\n--- Found source addon, creating temporary zip for: ${addonId} ---`));
            const tempZipPath = path.join(os.tmpdir(), `${addonId}_temp_check_${Date.now()}.zip`);
            try {
                const output = fs.createWriteStream(tempZipPath);
                const archive = archiver('zip', { zlib: { level: 9 } });
                await new Promise((resolve, reject) => {
                    output.on('close', resolve);
                    archive.on('error', reject);
                    archive.pipe(output);
                    archive.directory(sourcePath, addonId);
                    archive.finalize();
                });

                logger.info(`Temporary zip created at: ${tempZipPath}`);
                const addonInfo = getAddonIdFromZip(tempZipPath);
                if (addonInfo) {
                    await blenderCheck(tempZipPath, addonInfo.id, addonInfo.isLegacy);
                }
            } catch (e) {
                logger.error(`Failed to create or check temporary zip for ${addonId}: ${e.message}`);
            } finally {
                if (await fs.pathExists(tempZipPath)) {
                    await fs.remove(tempZipPath);
                    logger.debug(`Removed temporary zip: ${tempZipPath}`);
                }
            }
        }

        const stats = await fs.stat(inputPath);
        if (stats.isDirectory()) {
            logger.info(`Scanning directory for addons: ${inputPath}`);
            
            // 1. Find all source directories
            const sourceDirs = new Set();
            const manifestFiles = await glob('**/blender_manifest.toml', { cwd: inputPath, absolute: true, nodir: true });
            manifestFiles.forEach(file => sourceDirs.add(path.dirname(file)));
            
            const initFiles = await glob('**/__init__.py', { cwd: inputPath, absolute: true, nodir: true });
            initFiles.forEach(file => sourceDirs.add(path.dirname(file)));

            // Filter out sub-addons (e.g. if we have 'addon/' and 'addon/modules/', only keep 'addon/')
            const sortedDirs = [...sourceDirs].sort((a, b) => a.length - b.length);
            const finalDirs = [];
            for (const dir of sortedDirs) {
                if (!finalDirs.some(parent => dir.startsWith(parent + path.sep) && dir !== parent)) {
                    finalDirs.push(dir);
                }
            }
            
            for (const dir of finalDirs) {
                await checkSourceDirectory(dir);
            }

            // 2. Find all package files
            const packageFiles = await glob('**/*.@(zip|whl)', { cwd: inputPath, absolute: true, nodir: true });
            for (const fullPath of packageFiles) {
                const addonInfo = getAddonIdFromZip(fullPath);
                if (addonInfo) {
                    await blenderCheck(fullPath, addonInfo.id, addonInfo.isLegacy);
                } else {
                    logger.warn(`Could not determine addon ID for ${path.basename(fullPath)}, skipping.`);
                }
            }

        } else if (inputPath.endsWith('.zip') || inputPath.endsWith('.whl')) {
            const addonInfo = getAddonIdFromZip(inputPath);
            if (addonInfo) {
                await blenderCheck(inputPath, addonInfo.id, addonInfo.isLegacy);
            } else {
                logger.error('Could not determine addon ID from the provided file.');
            }
        } else {
            logger.warn(`Input path is not a directory or a supported package file: ${inputPath}`);
        }

        logger.success(chalk.cyan.bold(`--- Standalone Blender Check Finished ---`));
    })
    .demandCommand(1, 'You must provide a command.')
    .help()
    .argv;
