from .steel_lib import *

# Auto-generated importlib logic for compiled extensions
try:
    import importlib.util
    import sys
    import os
    from pathlib import Path

    addon_dir = Path(__file__).parent

    # List of expected compiled extensions (without platform-specific suffixes)
    expected_extensions = ["execution_time_extended","ops_fast_normals_extended","ops_data_removal_extended","ui_exceptions_extended","ops_simplify_extended","ops_settings_modifiers_extended","menu_simplify_extended"]

    for base_name in expected_extensions:
        extension_path = None

        # Look for compiled extension files with various suffixes
        for ext_file in addon_dir.glob(f'{base_name}.*'):
            if ext_file.suffix in ['.so', '.pyd', '.dylib'] or '.so' in ext_file.name:
                extension_path = str(ext_file)
                break

        if extension_path:
            try:
                spec = importlib.util.spec_from_file_location(base_name, extension_path)
                if spec and spec.loader:
                    module = importlib.util.module_from_spec(spec)
                    sys.modules[base_name] = module
                    spec.loader.exec_module(module)

                    # Import all public attributes from the compiled module
                    for attr_name in dir(module):
                        if not attr_name.startswith('_'):
                            globals()[attr_name] = getattr(module, attr_name)
                else:
                    print(f'Warning: Could not create spec for {extension_path}')
            except Exception as e:
                print(f'Warning: Failed to load {base_name} compiled extension: {e}')
        else:
            print(f'Warning: Could not find compiled extension file for {base_name}')

except ImportError as ie:
    print(f'Warning: Failed to import required modules for extension loading: {ie}')

bl_info = {'name': 'Mustard Simplify', 'description': 'A set of tools to simplify scenes for better viewport performance', 'author': 'Mustard', 'version': (2025, 2, 0), 'blender': (4, 3, 0), 'warning': '', 'doc_url': 'https://github.com/Mustard2/MustardSimplify/wiki', 'category': 'Scene'}
from . import settings
from . import simplify
from . import tools
from . import utils
from . import menu

def register():
    settings.register()
    simplify.register()
    tools.register()
    utils.register()
    menu.register()

def unregister():
    menu.unregister()
    utils.unregister()
    tools.unregister()
    simplify.unregister()
    settings.unregister()